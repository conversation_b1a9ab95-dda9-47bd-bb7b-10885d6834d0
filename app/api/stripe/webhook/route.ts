import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { createClient } from '@supabase/supabase-js'
import Stripe from 'stripe'
import { SupabaseClient } from '@supabase/supabase-js'
import { handleStoryVenturePayment } from '@/lib/stripe/webhook-handlers'

// Webhook secrets for platform and connected accounts
const platformWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET_PLATFORM
const connectWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET_CONNECT

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = request.headers.get('stripe-signature')

  if (!signature) {
    console.error('Missing Stripe signature')
    return NextResponse.json({ error: 'Missing signature' }, { status: 400 })
  }

  if (!platformWebhookSecret && !connectWebhookSecret) {
    console.error('Missing webhook secrets - check environment variables')
    return NextResponse.json({ error: 'Missing webhook secret' }, { status: 400 })
  }

  let event: Stripe.Event

  // Try to verify with platform secret first, then connect secret
  try {
    if (platformWebhookSecret) {
      event = stripe.webhooks.constructEvent(body, signature, platformWebhookSecret)
    } else {
      throw new Error('No platform secret')
    }
  } catch (platformError) {
    try {
      if (connectWebhookSecret) {
        event = stripe.webhooks.constructEvent(body, signature, connectWebhookSecret)
      } else {
        throw new Error('No connect secret')
      }
    } catch (connectError) {
      console.error('Webhook signature verification failed with both secrets:', { platformError, connectError })
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }
  }

  // Use service role to bypass RLS for webhook operations
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )

  try {
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        await handlePaymentSuccess(paymentIntent, supabase)
        break
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        await handlePaymentFailure(paymentIntent, supabase)
        break
      }

      case 'account.updated': {
        const account = event.data.object as Stripe.Account
        await handleAccountUpdate(account, supabase)
        break
      }

      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        await handleCheckoutSessionCompleted(session, supabase)
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        await handleSubscriptionPayment(invoice, supabase)
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        await handleSubscriptionPaymentFailed(invoice, supabase)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 })
  }
}

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent, supabase: SupabaseClient) {
  const metadata = paymentIntent.metadata
  const payerId = metadata.payer_id
  const writerId = metadata.writer_id
  const paymentType = metadata.payment_type
  const platformFee = parseInt(metadata.platform_fee || '0')
  const writerAmount = parseInt(metadata.writer_amount || '0')

  console.log('Processing payment success:', {
    paymentIntentId: paymentIntent.id,
    payerId,
    writerId,
    paymentType,
    totalAmount: paymentIntent.amount,
    platformFee,
    writerAmount
  })

  // Record payment in database
  const { data: payment, error: paymentError } = await supabase
    .from('payments')
    .insert({
      stripe_payment_id: paymentIntent.id,
      payer_id: payerId,
      writer_id: writerId,
      amount_cents: paymentIntent.amount,
      kind: paymentType
    })
    .select()
    .single()

  if (paymentError) {
    console.error('Error recording payment:', paymentError)
    return
  }

  console.log('Payment recorded successfully:', payment.id)

  // Handle Story Ventures payments
  await handleStoryVenturePayment(paymentIntent)

  if (paymentType === 'subscription') {
    // Grant 30 post credits
    const expiresAt = new Date()
    expiresAt.setMonth(expiresAt.getMonth() + 6) // Credits expire in 6 months

    const { error: creditsError } = await supabase
      .from('post_credits')
      .upsert({
        user_id: payerId,
        writer_id: writerId,
        credits_remaining: 30,
        credits_total: 30,
        payment_id: payment.id,
        expires_at: expiresAt.toISOString()
      })

    if (creditsError) {
      console.error('Error granting credits:', creditsError)
    } else {
      console.log('Credits granted successfully')
    }
  } else if (paymentType === 'donation') {
    // Record donation
    const { error: donationError } = await supabase
      .from('donations')
      .insert({
        donor_id: payerId,
        writer_id: writerId,
        payment_id: payment.id,
        amount: paymentIntent.amount,
        message: metadata.donation_message || null,
        is_anonymous: false
      })

    if (donationError) {
      console.error('Error recording donation:', donationError)
    } else {
      console.log('Donation recorded successfully')
    }
  }
}

async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent, supabase: SupabaseClient) {
  const metadata = paymentIntent.metadata

  // Record failed payment
  await supabase
    .from('payments')
    .insert({
      stripe_payment_intent_id: paymentIntent.id,
      payer_id: metadata.payer_id,
      writer_id: metadata.writer_id,
      amount_total: paymentIntent.amount,
      amount_writer: parseInt(metadata.writer_amount),
      amount_platform: parseInt(metadata.platform_fee),
      payment_type: metadata.payment_type,
      status: 'failed',
      metadata: metadata
    })
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session, supabase: SupabaseClient) {
  const metadata = session.metadata
  if (!metadata) return

  console.log('Checkout session completed:', session.id, metadata)

  // Handle book purchases
  if (metadata.type === 'book_purchase') {
    await handleBookPurchase(session, supabase)
    return
  }

  // Handle one-time payments (donations and credit purchases)
  if (metadata.payment_type === 'donation' || metadata.payment_type === 'subscription') {
    // Payment intent will be handled separately by payment_intent.succeeded
    console.log('One-time payment completed, will be handled by payment_intent.succeeded')
  }
}

async function handleBookPurchase(session: Stripe.Checkout.Session, supabase: SupabaseClient) {
  const metadata = session.metadata
  if (!metadata) return

  const bookId = metadata.book_id
  const buyerId = metadata.buyer_id
  const authorId = metadata.author_id

  if (!bookId || !buyerId || !authorId) {
    console.error('Missing required metadata for book purchase:', metadata)
    return
  }

  console.log('Processing book purchase:', {
    sessionId: session.id,
    bookId,
    buyerId,
    authorId,
    amountTotal: session.amount_total
  })

  try {
    // Record the book purchase
    const { error: purchaseError } = await supabase
      .from('book_purchases')
      .insert({
        user_id: buyerId,
        project_id: bookId,
        purchase_price_cents: session.amount_total || 0,
        stripe_payment_id: session.payment_intent as string || session.id
      })

    if (purchaseError) {
      console.error('Error recording book purchase:', purchaseError)
      return
    }

    // Update the sales count in the projects table
    const { data: currentProject } = await supabase
      .from('projects')
      .select('sales_count')
      .eq('id', bookId)
      .single()

    const { error: salesUpdateError } = await supabase
      .from('projects')
      .update({
        sales_count: (currentProject?.sales_count || 0) + 1
      })
      .eq('id', bookId)

    if (salesUpdateError) {
      console.error('Error updating sales count:', salesUpdateError)
      // Don't fail the entire process if this fails
    }

    // Add book to user's library
    const { error: libraryError } = await supabase
      .from('user_library')
      .insert({
        user_id: buyerId,
        project_id: bookId,
        access_type: 'purchased'
      })

    if (libraryError) {
      console.error('Error adding book to library:', libraryError)
      // Don't fail the entire process if library addition fails
    }

    console.log('Book purchase processed successfully:', bookId)

  } catch (error) {
    console.error('Error processing book purchase:', error)
  }
}

async function handleSubscriptionPayment(invoice: Stripe.Invoice, supabase: SupabaseClient) {
  const subscription = invoice.subscription as string
  const customerId = invoice.customer as string

  console.log('Subscription payment succeeded:', {
    invoiceId: invoice.id,
    subscriptionId: subscription,
    customerId,
    amountPaid: invoice.amount_paid
  })

  // Handle recurring subscription payments
  // This would extend existing subscriptions or create new ones
  // Implementation depends on your subscription model
}

async function handleSubscriptionPaymentFailed(invoice: Stripe.Invoice, supabase: SupabaseClient) {
  const subscription = invoice.subscription as string

  console.log('Subscription payment failed:', {
    invoiceId: invoice.id,
    subscriptionId: subscription,
    attemptCount: invoice.attempt_count
  })

  // Handle failed subscription payments
  // You might want to notify the user or suspend access
}

async function handleAccountUpdate(account: Stripe.Account, supabase: SupabaseClient) {
  console.log('Account updated webhook received:', {
    accountId: account.id,
    detailsSubmitted: account.details_submitted,
    chargesEnabled: account.charges_enabled,
    payoutsEnabled: account.payouts_enabled,
    metadata: account.metadata
  })

  // Update user's Stripe onboarding status
  const isComplete = account.details_submitted &&
                    account.charges_enabled &&
                    account.payouts_enabled

  // Try to update by stripe_account_id first (most reliable)
  const { data: updatedUser, error: updateError } = await supabase
    .from('users')
    .update({
      stripe_onboarding_complete: isComplete
    })
    .eq('stripe_account_id', account.id)
    .select()

  if (updateError) {
    console.error('Error updating user onboarding status by account ID:', updateError)

    // Fallback: try to update by user_id from metadata
    const userId = account.metadata?.user_id
    if (userId) {
      const { error: fallbackError } = await supabase
        .from('users')
        .update({
          stripe_onboarding_complete: isComplete
        })
        .eq('id', userId)

      if (fallbackError) {
        console.error('Error updating user onboarding status by user ID:', fallbackError)
      } else {
        console.log('User onboarding status updated via fallback:', { userId, isComplete })
      }
    }
  } else {
    console.log('User onboarding status updated:', {
      accountId: account.id,
      isComplete,
      updatedUsers: updatedUser?.length || 0
    })
  }
}
